using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Enemies;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Enemies
{
    public abstract class NavMeshEnemyController<TEnemy> : EnemyController<TEnemy> where TEnemy : EnemyActor
    {
        private const int RequestStateAuthorityInterval = 5;

        [SerializeField] private NavMeshAgent agent;
        [SerializeField] private byte damage;
        [SerializeField] private float patrolSpeed;
        [SerializeField] private float patrolRange;
        [SerializeField] private float chaseSpeed;
        [SerializeField] private int scanTargetRadius;
        [SerializeField] private float attackRangeSqr;
        [SerializeField] private bool isAttachableToTarget;
        [SerializeField] private float rotationSpeed = 10;

        [Header("Intervals")]
        [SerializeField] private Vector2 chaseInterval;
        [SerializeField] private Vector2 damageInterval;
        [SerializeField] private Vector2 patrolInterval;
        [SerializeField] private Vector2 scanInterval;
        [SerializeField] private Vector2 audioInterval;

        private IXRPlayer xrPlayer;
        private PlayerActor targetPlayer;
        private PlayersModel playersModel;
        private INetworkClient networkClient;
        private Vector3 previousPosition;

        private float nextChaseTime;
        private float nextDamageTime;
        private float nextPatrolTime;
        private float nextScanTime;
        private float nextAudioTime;
        private float nextRequestStateAuthorityTime;

        private bool IsAlive => enemy.IsAlive;
        private bool HasTarget => targetPlayer != null;
        private bool CanChase => Time.time > nextChaseTime;
        private bool CanPatrol => Time.time > nextPatrolTime;
        private bool CanScan => Time.time > nextScanTime;
        private bool CanPlayAudioByState => Time.time > nextAudioTime;
        private bool CanAttack => HasTarget && (IsAttachedToTarget || (targetPlayer.transform.position - transform.position).sqrMagnitude < attackRangeSqr);
        private bool CanSetDamage => Time.time > nextDamageTime;

        private EnemyState State
        {
            get => enemy.State;
            set => enemy.State = value;
        }

        [Networked] private PlayerRef TargetPlayerRef { get; set; }
        [Networked] private NetworkBool IsAttachedToTarget { get; set; }

        [Inject]
        private void Construct(PlayersModel playersModel, IXRPlayer xrPlayer, INetworkClient networkClient)
        {
            this.xrPlayer = xrPlayer;
            this.playersModel = playersModel;
            this.networkClient = networkClient;
        }

        public override void Spawned()
        {
            base.Spawned();
            xrPlayer.OnPoseUpdated.Subscribe(_ => RenderLocalAttachedPose()).AddTo(DespawnCancellationToken);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            enemy.StopChaseAudio();
            agent.updateRotation = false;
            targetPlayer = null;
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            if (!IsAlive)
            {
                return;
            }

            TryEnableAgent();
            ScanTarget();
            UpdateStates();
            TrySwitchToIdle();
            TryClearTargetPlayer();
            UpdateRotation();
        }

        public override void Render()
        {
            base.Render();
            TryDisableAgent();
            TryRequestStateAuthority();
            PlayAudioByState();
            PlayChaseAudio();
            RenderRemoteAttachedPose();
            UpdateTargetPlayer();
        }

        private void TryEnableAgent()
        {
            if (agent.enabled)
            {
                return;
            }

            var position = transform.position;
            agent.enabled = true;
            agent.Warp(position);
        }

        private void TryDisableAgent()
        {
            if (!HasStateAuthority && agent.enabled)
            {
                agent.ResetPath();
                agent.enabled = false;
            }
        }

        private void TryClearTargetPlayer()
        {
            if (TargetPlayerRef != PlayerRef.None && ((HasTarget && !targetPlayer.IsAlive) || !networkClient.HasPlayer(TargetPlayerRef)))
            {
                TargetPlayerRef = PlayerRef.None;

                if (IsAttachedToTarget)
                {
                    IsAttachedToTarget = false;
                }
            }
        }

        private void ScanTarget()
        {
            if (!CanScan)
            {
                return;
            }

            if (playersModel.TryFindClosestPlayer(transform.position, scanTargetRadius, IsPlayerTargetable, out var player))
            {
                if (TargetPlayerRef != player.StateAuthority)
                {
                    TargetPlayerRef = player.StateAuthority;
                }
            }
            else
            {
                if (TargetPlayerRef != PlayerRef.None)
                {
                    TargetPlayerRef = PlayerRef.None;
                }
            }

            nextScanTime = Time.time + Random.Range(scanInterval.x, scanInterval.y);
        }

        private void UpdateStates()
        {
            switch (State)
            {
                case EnemyState.Idle:
                    UpdateIdleState();
                    break;
                case EnemyState.Patrol:
                    UpdatePatrolState();
                    break;
                case EnemyState.Chase:
                    UpdateChaseState();
                    break;
                case EnemyState.Attack:
                    UpdateAttackState();
                    break;
            }
        }

        private void UpdateIdleState()
        {
            if (CanAttack)
            {
                State = EnemyState.Attack;
            }
            else if (HasTarget)
            {
                State = EnemyState.Chase;
            }
            else if (CanPatrol)
            {
                State = EnemyState.Patrol;
            }
        }

        private void UpdatePatrolState()
        {
            if (HasTarget)
            {
                State = EnemyState.Chase;
            }
            else
            {
                Patrol();
            }
        }

        private void UpdateChaseState()
        {
            if (!HasTarget)
            {
                State = EnemyState.Patrol;
            }
            else if (CanAttack)
            {
                State = EnemyState.Attack;
            }
            else
            {
                Chase();
            }
        }

        private void UpdateAttackState()
        {
            if (CanAttack)
            {
                Attack();
            }
            else
            {
                State = EnemyState.Chase;
            }
        }

        private void Chase()
        {
            if (CanChase)
            {
                agent.speed = chaseSpeed;
                agent.SetDestination(targetPlayer.transform.position);
                nextChaseTime = Time.time + Random.Range(chaseInterval.x, chaseInterval.y);
            }
        }

        private void Patrol()
        {
            if (CanPatrol && TryGetPatrolPoint(out var point))
            {
                agent.speed = patrolSpeed;
                agent.SetDestination(point);
                nextPatrolTime = Time.time + Random.Range(patrolInterval.x, patrolInterval.y);
            }
        }

        private void Attack()
        {
            if (CanSetDamage)
            {
                if (isAttachableToTarget)
                {
                    if (!IsAttachedToTarget)
                    {
                        IsAttachedToTarget = true;
                    }

                    agent.ResetPath();
                }

                targetPlayer.SetDamageByMonsterRpc(damage);
                nextAudioTime = 0;
                nextDamageTime = Time.time + Random.Range(damageInterval.x, damageInterval.y);
            }
        }

        private void TrySwitchToIdle()
        {
            if (!HasTarget && agent.hasPath && agent.remainingDistance < 0.5f)
            {
                agent.ResetPath();
                State = EnemyState.Idle;
            }
        }

        private bool IsPlayerTargetable(PlayerActor player)
        {
            return player.IsValid && player.IsAlive;
        }

        private void PlayAudioByState()
        {
            if (!CanPlayAudioByState)
            {
                return;
            }

            switch (State)
            {
                case EnemyState.Idle:
                case EnemyState.Patrol:
                    enemy.PlayIdleOrPatrolAudio();
                    break;
                case EnemyState.Attack:
                    enemy.PlayAttackAudio();
                    break;
            }

            nextAudioTime = Time.time + Random.Range(audioInterval.x, audioInterval.y);
        }

        private void PlayChaseAudio()
        {
            if (State == EnemyState.Chase)
            {
                enemy.PlayChaseAudio();
            }
            else
            {
                enemy.StopChaseAudio();
            }
        }

        private bool TryGetPatrolPoint(out Vector3 point)
        {
            var randomPoint = patrolRange * Random.onUnitSphere;
            var shiftPoint = transform.position + new Vector3(randomPoint.x, 0, randomPoint.z);
            if (NavMesh.SamplePosition(shiftPoint, out var hit, 5, NavMesh.AllAreas))
            {
                point = hit.position;
                return true;
            }

            point = Vector3.zero;
            return false;
        }

        private void TryRequestStateAuthority()
        {
            if ((CanRequestStateAuthorityIfNone() || CanRequestStateAuthorityIfSelfTarget()) && Time.time > nextRequestStateAuthorityTime)
            {
                Object.RequestStateAuthority();
                nextRequestStateAuthorityTime = Time.time + RequestStateAuthorityInterval;
            }
        }

        private bool CanRequestStateAuthorityIfNone()
        {
            return Runner.IsSharedModeMasterClient && (StateAuthority == PlayerRef.None || StateAuthority == PlayerRef.Invalid);
        }

        private bool CanRequestStateAuthorityIfSelfTarget()
        {
            return HasTarget && !HasStateAuthority && targetPlayer == playersModel.LocalPlayer.Value;
        }

        private void RenderLocalAttachedPose()
        {
            if (!isAttachableToTarget || !HasStateAuthority)
            {
                return;
            }

            RenderAttachedPose();
        }

        private void RenderRemoteAttachedPose()
        {
            if (!isAttachableToTarget || HasStateAuthority)
            {
                return;
            }

            RenderAttachedPose();
        }

        private void RenderAttachedPose()
        {
            if (HasTarget && IsAttachedToTarget && targetPlayer.IsAlive)
            {
                var node = targetPlayer.HeadNode;
                var rotation = node.rotation * Quaternion.Euler(enemy.OffsetRotation);
                var position = node.position + enemy.OffsetTranslation.z * node.forward + enemy.OffsetTranslation.y * node.up;
                transform.SetPositionAndRotation(position, rotation);
            }
        }

        private void UpdateTargetPlayer()
        {
            if (TargetPlayerRef == PlayerRef.None && targetPlayer != null)
            {
                targetPlayer = null;
            }
            else if (TargetPlayerRef != PlayerRef.None && (targetPlayer == null || targetPlayer.StateAuthority != TargetPlayerRef))
            {
                if (playersModel.TryGetPlayer(TargetPlayerRef.PlayerId, out var player))
                {
                    targetPlayer = player;
                }
            }
        }

        private void UpdateRotation()
        {
            var delta = (transform.position - previousPosition).OnlyXZ();
            if (delta != Vector3.zero)
            {
                transform.rotation = Quaternion.Slerp(transform.rotation, Quaternion.LookRotation(delta, Vector3.up), Runner.DeltaTime * rotationSpeed);
            }

            previousPosition = transform.position;
        }
    }
}