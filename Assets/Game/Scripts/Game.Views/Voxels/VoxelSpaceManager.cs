using System;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Models;
using Game.Views.LevelDefinitions;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VoxelPlay;
using Object = UnityEngine.Object;

namespace Game.Views.Voxels
{
    public partial class VoxelSpaceManager : IDisposable
    {
        private bool spreadingEnabled;
        private readonly VoxelModel voxelModel;
        private readonly VoxelConfig voxelConfig;
        private readonly IAudioClient audioClient;
        private readonly VoxelPreviewActor voxelPreview;
        private readonly VoxelPlayEnvironment voxelEnvironment;

        private readonly CancellationTokenSource disposeCancellationTokenSource = new();
        private readonly IAsyncReactiveProperty<bool> isMapLoaded = new AsyncReactiveProperty<bool>(false);
        private CancellationTokenSource voxelWorldLoadedCancellationTokenSource;
        private CancellationTokenSource backgroundMusicCancellationTokenSource;

        public Vector3 PlaceVoxelPoint { get; set; }
        public bool IsInitialized => voxelEnvironment.initialized;
        public IReadOnlyAsyncReactiveProperty<bool> IsMapLoaded => isMapLoaded;

        public VoxelSpaceManager(VoxelConfig voxelConfig, IAudioClient audioClient, VoxelModel voxelModel, INetworkClient networkClient)
        {
            this.audioClient = audioClient;
            this.voxelConfig = voxelConfig;
            this.voxelModel = voxelModel;

            voxelEnvironment = VoxelPlayEnvironment.instance;
            voxelEnvironment.cameraMain = Camera.main;
            voxelEnvironment.distanceAnchor = Camera.main.transform;

            voxelEnvironment.OnWorldLoaded += HandleWorldLoaded;
            voxelEnvironment.OnVoxelAfterSpread += HandleVoxelEnvironmentSpread;
            voxelEnvironment.OnVoxelBeforeSpread += HandleVoxelBeforeSpread;

            voxelPreview = Object.Instantiate(voxelConfig.VoxelPreviewPrefab);
            voxelPreview.SetActive(false);

            networkClient.OnNetworkActorSpawned.Subscribe(x => HandleNetworkActorSpawned(x.actor)).AddTo(disposeCancellationTokenSource.Token);
            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(disposeCancellationTokenSource.Token);
        }

        public void Dispose()
        {
            disposeCancellationTokenSource.CancelAndDispose();
            backgroundMusicCancellationTokenSource.CancelAndDispose();
            voxelEnvironment.OnWorldLoaded -= HandleWorldLoaded;
            voxelEnvironment.OnVoxelAfterSpread -= HandleVoxelEnvironmentSpread;
            voxelEnvironment.OnVoxelBeforeSpread -= HandleVoxelBeforeSpread;
        }

        public void InitializeMap(LevelDefinition levelDefinition, int worldExtents, byte[] mapData)
        {
            var settings = levelDefinition.VoxelEnvironmentSettings;
            var validMapData = mapData ?? settings.mapData?.bytes;
            var voxelDefId = voxelConfig.TryGetVoxelDefId(settings.defaultVoxelDefinition, out var id) ? id : 0;
            var voxelWorldExtents = worldExtents > 0
                ? new Vector3(worldExtents, levelDefinition.BoundsSettings.worldExtents.y, worldExtents)
                : levelDefinition.BoundsSettings.worldExtents;

            voxelEnvironment.world = settings.worldDefinition;
            voxelEnvironment.world.moreVoxels = voxelConfig.AllVoxelDefList;
            voxelEnvironment.world.gravity = CoreConstants.WorldScaleGravity.y;
            voxelEnvironment.world.extents = voxelWorldExtents;
            voxelEnvironment.visibleChunksDistance = settings.visibleChunkDistance;
            voxelEnvironment.enableTrees = settings.useTrees;
            voxelEnvironment.enableVegetation = settings.useVegetation;
            voxelEnvironment.enableDetailGenerators = settings.useDetailGeneration;
            voxelEnvironment.enableNavMesh = settings.useNavMesh;
            voxelEnvironment.unloadFarNavMesh = settings.unloadFarNavMesh;
            voxelEnvironment.ambientLight = settings.ambientLight;
            voxelEnvironment.daylightShadowAtten = settings.daylightShadowAtten;
            voxelEnvironment.diffuseWrap = settings.diffuseWrap;
            voxelEnvironment.enableFogSkyBlending = settings.useFog;
            voxelEnvironment.fogTint = settings.useFog ? settings.fogTint : Color.white;

            if (settings.useFog)
            {
                voxelEnvironment.fogDistance = settings.fogDistance;
                voxelEnvironment.fogTint = settings.fogTint;
                voxelEnvironment.fogFallOff = settings.fogFallOff;
                voxelEnvironment.fogAmount = settings.fogHeight;
            }

            RenderSettings.ambientSkyColor = settings.ambientColor;
            RenderSettings.sun.color = settings.sunColor;
            RenderSettings.sun.intensity = settings.sunIntensity;

            SetMap(validMapData);
            PlayBackgroundMusic(levelDefinition.SharedSettings.audioKey);
            voxelModel.SetSelectedVoxelId(voxelDefId);
        }

        public void UninitializeMap()
        {
            SetMapLoaded(false);
        }

        public void RenderPreviewVoxel(Vector3 origin, Vector3 point)
        {
            voxelPreview.Render(origin, point);
        }

        public void HidePreviewVoxel()
        {
            voxelPreview.Hide();
        }

        public bool TryPlayCollisionSound(Vector3 point)
        {
            if (!IsInitialized || voxelEnvironment.IsEmptyAtPosition(point) || !voxelEnvironment.GetVoxelIndex(point, out var chunk, out var voxelIndex, false))
            {
                return false;
            }

            var voxel = chunk.voxels[voxelIndex];

            if (voxel.typeIndex == 0)
            {
                return true;
            }

            var audioClipList = voxelEnvironment.GetVoxelDefinition(voxel.typeIndex).footfalls.ToList().FindAll(f => f != null);
            var audioKey = audioClipList is not { Count: > 0 } ? voxelConfig.VoxelTouchAudioKey : audioClipList.RandomItem().name;
            audioClient.Play(audioKey, point, disposeCancellationTokenSource.Token);

            return true;
        }

        public VoxelSubmergedArgs GetSubmergedArgs(Vector3 position)
        {
            if (!IsInitialized)
            {
                return null;
            }

            if (voxelEnvironment.GetVoxelIndex(position, out var chunk, out var voxelIndex, false))
            {
                var voxel = chunk.voxels[voxelIndex];
                if (voxelConfig.WaterVoxels != null)
                {
                    foreach (var voxelDef in voxelConfig.WaterVoxels)
                    {
                        if (voxel.type == voxelDef)
                        {
                            return GetVoxelSubmergedArgs(chunk, voxelIndex, voxel, false);
                        }
                    }
                }

                if (voxelConfig.LavaVoxels != null)
                {
                    foreach (var voxelDef in voxelConfig.LavaVoxels)
                    {
                        if (voxel.type == voxelDef)
                        {
                            return GetVoxelSubmergedArgs(chunk, voxelIndex, voxel, true);
                        }
                    }
                }
            }

            return null;
        }

        private VoxelSubmergedArgs GetVoxelSubmergedArgs(VoxelChunk chunk, int voxelIndex, Voxel voxel, bool isLava)
        {
            if (!IsInitialized)
            {
                return null;
            }

            float waterHeight;
            float waterLevel;
            var voxelPosition = voxelEnvironment.GetVoxelPosition(chunk, voxelIndex);
            var voxelAbove = voxelEnvironment.GetVoxel(voxelPosition + new Vector3(0f, 1f, 0f), false);
            if (voxelAbove == Voxel.Empty || (voxelAbove != Voxel.Empty && voxelAbove.type.renderType != RenderType.Water))
            {
                waterHeight = (float)voxelPosition.y - 0.5f + voxel.GetWaterLevel() / 15f;
                waterLevel = voxel.GetWaterLevel() / 15f;
            }
            else
            {
                waterHeight = (float)voxelPosition.y + 0.5f;
                waterLevel = 1f;
            }

            return new VoxelSubmergedArgs(voxel.type.playerDamage, waterHeight, isLava, waterLevel);
        }

        private void SetMapLoaded(bool isMapLoadedValue)
        {
            if (isMapLoaded.Value == isMapLoadedValue)
            {
                return;
            }

            voxelWorldLoadedCancellationTokenSource.CancelAndDispose();
            isMapLoaded.Value = isMapLoadedValue;
        }

        private void PlayBackgroundMusic(string backgroundMusicKey)
        {
            backgroundMusicCancellationTokenSource.CancelAndDispose();

            if (string.IsNullOrEmpty(backgroundMusicKey))
            {
                GameLogger.Voxels.Warn("Background music key is null or empty");
                return;
            }

            backgroundMusicCancellationTokenSource = new CancellationTokenSource();
            audioClient.Play(backgroundMusicKey, Vector3.zero, backgroundMusicCancellationTokenSource.Token);
        }

        private void HandleWorldLoaded()
        {
            HandleWorldLoadedAsync().Forget();
        }

        private async UniTaskVoid HandleWorldLoadedAsync()
        {
            voxelWorldLoadedCancellationTokenSource.CancelAndDispose();
            voxelWorldLoadedCancellationTokenSource = new CancellationTokenSource();
            await UniTask.Yield(voxelWorldLoadedCancellationTokenSource.Token);
            SetMapLoaded(true);
        }

        private void HandleVoxelEnvironmentSpread(VoxelDefinition voxelDefinition, Vector3d newPosition)
        {
            if (!IsInitialized)
            {
                return;
            }

            var oneDown = newPosition + Vector3d.down;
            var twoDown = newPosition + Vector3d.down + Vector3d.down;
            var threeDown = newPosition + Vector3d.down + Vector3d.down + Vector3d.down;

            if (voxelEnvironment.IsEmptyAtPosition(oneDown) && voxelEnvironment.IsEmptyAtPosition(twoDown) && voxelEnvironment.IsEmptyAtPosition(threeDown))
            {
                DestroyVoxel(newPosition);
            }
        }

        private bool HandleVoxelBeforeSpread(VoxelDefinition voxelDefinition, Vector3d newPosition, VoxelDefinition voxelOnNewPosition)
        {
            return spreadingEnabled;
        }

        private void HandleNetworkActorSpawned(NetworkActor actor)
        {
            AddVoxelsMasterClientObject(actor);
        }

        private void HandleShutdown()
        {
            RemoveVoxelsMasterClientObject();
        }
    }
}