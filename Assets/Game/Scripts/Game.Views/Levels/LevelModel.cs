using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Services;
using Game.Views.LevelDefinitions;
using Game.Views.Network;
using Game.Views.UI.Screens.JoinPublicLevel;
using Game.Views.Voxels;
using Modules.CloudRequest;
using Modules.Core;

namespace Game.Views.Levels
{
    public class LevelModel : ModelBase
    {
        private readonly GameConfig gameConfig;
        private readonly VoxelConfig voxelConfig;
        private readonly NetworkWire networkWire;
        private readonly LevelsConfig levelsConfig;
        private readonly ILevelService levelService;
        private readonly LevelDefinitionsManager levelDefinitionsManager;
        private readonly LevelListWidgetDataComparer levelListWidgetDataComparer;
        private readonly List<LevelListWidgetData> cachedLevelListWidgetDataList = new(50);

        private readonly IAsyncReactiveProperty<bool> isLevelLoading = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<List<LevelSessionRef>> levelSessionList = new AsyncReactiveProperty<List<LevelSessionRef>>(null);

        private readonly ISubject<Unit> onMapSaved = new Subject<Unit>();
        private readonly ISubject<bool> onLevelLoaded = new Subject<bool>();
        private readonly ISubject<LoadLevelArgs> onLevelStarting = new Subject<LoadLevelArgs>();
        private readonly ISubject<Unit> onLevelListUpdated = new Subject<Unit>();

        public LevelData Level { get; private set; }
        public bool IsLobbyLevel => Level.id == LobbyLevelId;
        public bool IsMinesLevel => Level.id == gameConfig.MinesLevelId;
        public bool IsDarkForestLevel => Level.id == gameConfig.DarkForestId;
        public List<LevelMetaData> LevelList { get; } = new();
        public string LobbyLevelId => gameConfig.LobbyLevelId;
        public LevelConfig LevelConfig => levelsConfig.GetLevelSettings(Level.gameMode);
        public LevelDefinition LevelDefinition => levelDefinitionsManager.LevelDefinition.Value;
        public bool HasLevelDefinition => levelDefinitionsManager.HasLevelDefinition;

        public IReadOnlyAsyncReactiveProperty<bool> IsLevelLoading => isLevelLoading;

        public IObservable<Unit> OnMapSaved => onMapSaved;
        public IObservable<bool> OnLevelLoaded => onLevelLoaded;
        public IObservable<LoadLevelArgs> OnLevelStarting => onLevelStarting;
        public IObservable<Unit> OnLevelListUpdated => onLevelListUpdated;

        public LevelModel(
            GameConfig gameConfig,
            NetworkWire networkWire,
            VoxelConfig voxelConfig,
            LevelsConfig levelsConfig,
            ILevelService levelService,
            LevelDefinitionsManager levelDefinitionsManager
        )
        {
            this.gameConfig = gameConfig;
            this.networkWire = networkWire;
            this.voxelConfig = voxelConfig;
            this.levelService = levelService;
            this.levelsConfig = levelsConfig;
            this.levelDefinitionsManager = levelDefinitionsManager;
            levelListWidgetDataComparer = new LevelListWidgetDataComparer(100);
            Level = GetLobbyLevel();

            networkWire.OnMapSaved.Subscribe(onMapSaved).AddTo(DisposeCancellationToken);
        }

        public void StartLevel(LoadLevelArgs args)
        {
            onLevelStarting.OnNext(args);
        }

        public void StartLobbyLevel()
        {
            StartLevel(new LoadLevelArgs(LobbyLevelId));
        }

        public async UniTask<Response<LevelContract>> CreateLevel(string name, string password, int templateId, int extents, GameMode gameMode, string creator, CancellationToken cancellationToken)
        {
            var response = await levelService.CreateLevel(name, password, templateId, extents, gameMode, creator, cancellationToken);
            if (response.IsFail)
            {
                GameLogger.Level.Warn("Unable to create level. Error: {0}", response.Info);
            }

            return response;
        }

        public async UniTask<Response<LevelContract>> LoadLevel(string id, string password, CancellationToken cancellationToken)
        {
            UpdateLevelLoading(true);

            var response = await levelService.GetLevel(id, password, cancellationToken);
            if (response.IsOk)
            {
                Level = LevelDataParser.ToLevelData(response.Data);
                CreateLevelDefinition();
            }

            UpdateLevelLoading(false);
            onLevelLoaded.OnNext(response.IsOk);

            return response;
        }

        public async UniTask<Response<LevelContract>> GetLevel(string id, string password, CancellationToken cancellationToken)
        {
            var response = await levelService.GetLevel(id, password, cancellationToken);
            if (!response.IsOk)
            {
                GameLogger.Level.Warn("Unable to load level. Error: {0}", response.Info);
            }

            return response;
        }

        public async UniTask<byte[]> LoadMap(CancellationToken cancellationToken)
        {
            var mapUrlResponse = await levelService.GetMapDownloadUrl(Level.id, cancellationToken);
            if (mapUrlResponse.IsFail)
            {
                return null;
            }

            if (string.IsNullOrEmpty(mapUrlResponse.Data))
            {
                GameLogger.Level.Warn("Unable to load map. Map url is null or empty");
                return null;
            }

            var mapDataResponse = await levelService.GetMap(mapUrlResponse.Data, cancellationToken);
            if (mapDataResponse.IsFail)
            {
                GameLogger.Level.Warn("Unable to load map. Error: {0}", mapDataResponse.Info);
                return null;
            }

            if (mapDataResponse.Data != null)
            {
                GameLogger.Level.Debug("Loaded map size: {0} Kb", mapDataResponse.Data.Length / 1024);
            }

            return mapDataResponse.Data;
        }

        public async UniTask<byte[]> LoadSliceMap(string instanceId, CancellationToken cancellationToken)
        {
            var mapUrlResponse = await levelService.GetSliceMapDownloadUrl(Level.id, instanceId, cancellationToken);
            if (mapUrlResponse.IsFail)
            {
                return null;
            }

            if (string.IsNullOrEmpty(mapUrlResponse.Data))
            {
                GameLogger.Level.Warn("Unable to load slice map. Slice map url is null or empty");
                return null;
            }

            var mapDataResponse = await levelService.GetMap(mapUrlResponse.Data, cancellationToken);
            if (mapDataResponse.IsFail)
            {
                GameLogger.Level.Warn("Unable to load slice map. Error: {0}", mapDataResponse.Info);
                return null;
            }

            if (mapDataResponse.Data != null)
            {
                GameLogger.Level.Debug("Loaded slice map size: {0} Kb", mapDataResponse.Data.Length / 1024);
            }

            return mapDataResponse.Data;
        }

        public async UniTask<bool> SaveMap(byte[] map, bool isUpdateLevelLoading)
        {
            if (map == null || map.Length == 0)
            {
                return false;
            }

            UpdateLevelLoading(true, isUpdateLevelLoading);

            var mapUrlResponse = await levelService.GetMapUploadUrl(Level.id, DisposeCancellationToken);
            if (mapUrlResponse.IsFail)
            {
                UpdateLevelLoading(false, isUpdateLevelLoading);
                return false;
            }

            if (string.IsNullOrEmpty(mapUrlResponse.Data))
            {
                GameLogger.Level.Warn("Unable to save map. Map url is null or empty");
                UpdateLevelLoading(false, isUpdateLevelLoading);
                return false;
            }

            var uploadMapResponse = await levelService.UploadMap(mapUrlResponse.Data, map, DisposeCancellationToken);
            if (uploadMapResponse.IsFail)
            {
                GameLogger.Level.Warn("Unable to save map. Error: {0}", uploadMapResponse.Info);
                UpdateLevelLoading(false, isUpdateLevelLoading);
                return false;
            }

            GameLogger.Level.Debug("Saved map size: {0} Kb", map.LongLength / 1024);

            UpdateLevelLoading(false, isUpdateLevelLoading);
            networkWire.SendMapSaved();
            return true;
        }

        public async UniTask<bool> SaveSliceMap(string instanceId, byte[] map)
        {
            if (map == null || map.Length == 0)
            {
                return false;
            }

            var mapUrlResponse = await levelService.GetSliceMapUploadUrl(Level.id, instanceId, DisposeCancellationToken);
            if (mapUrlResponse.IsFail)
            {
                return false;
            }

            if (string.IsNullOrEmpty(mapUrlResponse.Data))
            {
                GameLogger.Level.Warn("Unable to save slice map. Map url is null or empty");
                return false;
            }

            var uploadMapResponse = await levelService.UploadMap(mapUrlResponse.Data, map, DisposeCancellationToken);
            if (uploadMapResponse.IsFail)
            {
                GameLogger.Level.Warn("Unable to save slice map. Error: {0}", uploadMapResponse.Info);
                return false;
            }

            return true;
        }

        public async UniTask<bool> PublishLevel(bool isPublished)
        {
            UpdateLevelLoading(true);

            var response = await levelService.UpdateLevel(Level.id, new UpdateLevelData { isPublished = isPublished }, DisposeCancellationToken);

            UpdateLevelLoading(false);

            return response.IsOk;
        }

        public async UniTask<bool> DisableBuilding(bool isBuildingDisabled)
        {
            UpdateLevelLoading(true);

            var response = await levelService.UpdateLevel(Level.id, new UpdateLevelData { buildingDisabled = isBuildingDisabled }, DisposeCancellationToken);

            UpdateLevelLoading(false);

            return response.IsOk;
        }

        public async UniTask UpdateLevelList()
        {
            var levelIdList = levelSessionList.Value?.FindAll(x => !x.isPrivate).ConvertAll(x => x.levelId);
            var response = await levelService.GetLevelList(levelIdList, DisposeCancellationToken);

            if (response.IsOk && response.Data != null)
            {
                LevelList.Clear();
                LevelList.AddRange(response.Data.ConvertAll(LevelDataParser.ToLevelMetaData));

                onLevelListUpdated.OnNext(Unit.Default);
            }
        }

        public async UniTask<List<LevelMetaData>> GetLobbyLevelList(CancellationToken cancellationToken)
        {
            var lobbyLevelListId = gameConfig.LobbyPortalList.ConvertAll(x => x.levelId);

            if (!lobbyLevelListId.Contains(gameConfig.CustomPortalLevelId))
            {
                lobbyLevelListId.Add(gameConfig.CustomPortalLevelId);
            }

            if (lobbyLevelListId.Count == 0)
            {
                return new List<LevelMetaData>(0);
            }

            var response = await levelService.GetLobbyLevelList(lobbyLevelListId, cancellationToken);
            if (response.IsOk && response.Value is { Count: > 0 })
            {
                return response.Value;
            }

            return new List<LevelMetaData>(0);
        }

        public List<LevelListWidgetData> GetLevelListWidgetData(List<LevelMetaData> levelList)
        {
            cachedLevelListWidgetDataList.Clear();

            if (levelList == null || levelList.Count == 0)
            {
                return cachedLevelListWidgetDataList;
            }

            for (var i = 0; i < levelList.Count; i++)
            {
                var levelData = levelList[i];
                var widgetData = new LevelListWidgetData(levelData, voxelConfig.GetWorldTemplateIcon(levelData.worldTemplateId));

                if (levelData.id == Level.id)
                {
                    widgetData.isSelected.Value = true;
                }

                cachedLevelListWidgetDataList.Add(widgetData);
            }

            cachedLevelListWidgetDataList.Sort(levelListWidgetDataComparer);

            return cachedLevelListWidgetDataList;
        }

        public void SetLevelSessionList(List<LevelSessionRef> levelSessionListValue)
        {
            levelSessionList.Value = levelSessionListValue;
        }

        public LevelMetaData GetLevelMetaData(string levelId)
        {
            return LevelList?.Find(x => x.id == levelId);
        }

        private void UpdateLevelLoading(bool isLevelLoadingValue, bool isUpdate = true)
        {
            if (!isUpdate || isLevelLoading.Value == isLevelLoadingValue)
            {
                return;
            }

            isLevelLoading.Value = isLevelLoadingValue;
        }

        private LevelData GetLobbyLevel()
        {
            return new LevelData { id = LobbyLevelId };
        }

        private void CreateLevelDefinition()
        {
            var levelDefinitionId = Level.levelDefinitionId;
            if (levelDefinitionId == LevelDefinitionId.None && Level.worldTemplateId != 0)
            {
                Enum.TryParse(Level.worldTemplateId.ToString(), out levelDefinitionId);
                Level.levelDefinitionId = levelDefinitionId;
            }

            levelDefinitionsManager.CreateLevelDefinition(levelDefinitionId);
        }
    }
}